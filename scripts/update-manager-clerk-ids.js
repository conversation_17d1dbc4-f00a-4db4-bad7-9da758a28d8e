#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')

// Environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   NEXT_PUBLIC_SUPABASE_URL')
  console.error('   SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Manager update mappings
const managerUpdates = [
  {
    oldEmail: '<EMAIL>',
    newEmail: '<EMAIL>',
    newClerkId: null, // To be filled when <PERSON> signs in
    fullName: 'Greg'
  },
  {
    oldEmail: '<EMAIL>', 
    newEmail: '<EMAIL>',
    newClerkId: null, // To be filled when <PERSON> signs in
    fullName: 'Joe'
  },
  {
    oldEmail: '<EMAIL>',
    newEmail: '<EMAIL>', 
    newClerkId: null, // To be filled when Hannah signs in
    fullName: 'Hannah Hughes'
  }
]

async function updateManagerClerkId(oldEmail, newEmail, newClerkId, fullName) {
  console.log(`\n🔄 Updating manager: ${fullName}`)
  console.log(`   Old email: ${oldEmail}`)
  console.log(`   New email: ${newEmail}`)
  console.log(`   New Clerk ID: ${newClerkId}`)

  try {
    // Step 1: Get the old manager data
    const { data: oldManager, error: fetchError } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('email', oldEmail)
      .eq('active', false) // Should be inactive now
      .single()

    if (fetchError) {
      console.error(`❌ Failed to fetch old manager data: ${fetchError.message}`)
      return false
    }

    console.log(`📋 Found old manager record: ${oldManager.full_name}`)

    // Step 2: Check if new manager record already exists
    const { data: existingManager } = await supabase
      .from('appy_managers')
      .select('user_id')
      .eq('user_id', newClerkId)
      .single()

    if (existingManager) {
      console.log(`⚠️  Manager with Clerk ID ${newClerkId} already exists, skipping creation`)
    } else {
      // Step 3: Create new manager record with correct Clerk ID
      const { error: insertError } = await supabase
        .from('appy_managers')
        .insert({
          user_id: newClerkId,
          full_name: fullName,
          email: newEmail,
          department_id: oldManager.department_id,
          active: true,
          manager_id: oldManager.manager_id,
          role: oldManager.role
        })

      if (insertError) {
        console.error(`❌ Failed to create new manager record: ${insertError.message}`)
        return false
      }

      console.log(`✅ Created new manager record with Clerk ID`)
    }

    // Step 4: Update employee-manager relationships
    const { error: emUpdateError } = await supabase
      .from('appy_employee_managers')
      .update({ manager_id: newClerkId })
      .eq('manager_id', oldManager.user_id)

    if (emUpdateError) {
      console.error(`❌ Failed to update employee-manager relationships: ${emUpdateError.message}`)
    } else {
      console.log(`✅ Updated employee-manager relationships`)
    }

    // Step 5: Update appraisal manager references
    const { error: appraisalUpdateError } = await supabase
      .from('appy_appraisals')
      .update({ manager_id: newClerkId })
      .eq('manager_id', oldManager.user_id)

    if (appraisalUpdateError) {
      console.error(`❌ Failed to update appraisal manager references: ${appraisalUpdateError.message}`)
    } else {
      console.log(`✅ Updated appraisal manager references`)
    }

    // Step 6: Delete the old manager record (now that references are updated)
    const { error: deleteError } = await supabase
      .from('appy_managers')
      .delete()
      .eq('user_id', oldManager.user_id)

    if (deleteError) {
      console.error(`❌ Failed to delete old manager record: ${deleteError.message}`)
    } else {
      console.log(`✅ Deleted old manager record`)
    }

    console.log(`🎉 Successfully updated manager: ${fullName}`)
    return true

  } catch (error) {
    console.error(`❌ Error updating manager ${fullName}:`, error.message)
    return false
  }
}

async function updateSpecificManager(email, clerkId) {
  const manager = managerUpdates.find(m => m.newEmail === email)
  if (!manager) {
    console.error(`❌ No manager found for email: ${email}`)
    return false
  }

  return await updateManagerClerkId(
    manager.oldEmail,
    manager.newEmail,
    clerkId,
    manager.fullName
  )
}

async function showPendingUpdates() {
  console.log('📋 Pending manager updates:')
  console.log('=' .repeat(50))
  
  for (const manager of managerUpdates) {
    console.log(`\n👤 ${manager.fullName}`)
    console.log(`   Old: ${manager.oldEmail}`)
    console.log(`   New: ${manager.newEmail}`)
    console.log(`   Status: ${manager.newClerkId ? '✅ Ready to update' : '⏳ Waiting for sign-in'}`)
  }
  
  console.log('\n📝 Usage:')
  console.log('   node scripts/update-manager-clerk-ids.js <email> <clerk-id>')
  console.log('   Example: node scripts/update-manager-clerk-ids.js <EMAIL> user_abc123')
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    showPendingUpdates()
  } else if (args.length === 2) {
    const [email, clerkId] = args
    updateSpecificManager(email, clerkId)
      .then(success => {
        process.exit(success ? 0 : 1)
      })
      .catch(error => {
        console.error('Script failed:', error)
        process.exit(1)
      })
  } else {
    console.log('Usage: node scripts/update-manager-clerk-ids.js <email> <clerk-id>')
    console.log('   or: node scripts/update-manager-clerk-ids.js (to show pending updates)')
    process.exit(1)
  }
}

module.exports = { updateManagerClerkId, updateSpecificManager }
