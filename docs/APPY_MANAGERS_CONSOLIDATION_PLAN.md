# APPY_MANAGERS TABLE CONSOLIDATION PLAN (OPTION 3)
## Comprehensive Migration Strategy to Single Source of Truth

---

## 📋 EXECUTIVE SUMMARY

### Objective
Eliminate data duplication and architectural complexity by consolidating all person/employee data into the `appy_employees` table, retiring the `appy_managers` table for person data storage.

### Current Problems
1. **Data Duplication**: 33 people exist in both tables with different IDs
2. **ID Mismatch**: appy_employees uses UUIDs, appy_managers uses Clerk IDs
3. **Maintenance Burden**: Two sources of truth for the same person
4. **Sync Issues**: No automatic synchronization between tables
5. **Complex Queries**: Joins required for basic operations

### Solution Benefits
- ✅ **Single Source of Truth**: All person data in one table
- ✅ **No Duplicates**: Each person exists exactly once
- ✅ **Simplified Architecture**: Fewer joins, clearer data model
- ✅ **Better Performance**: Direct queries without complex joins
- ✅ **Easier Maintenance**: One place to update person information

---

## 🔍 CURRENT STATE ANALYSIS

### Database Impact Assessment

#### Tables with Foreign Key Dependencies (7 tables)
```sql
appy_appraisals.manager_id → appy_managers.user_id
appy_employee_feedback.reviewed_by → appy_managers.user_id
appy_employee_feedback.resolved_by → appy_managers.user_id
appy_employee_managers.manager_id → appy_managers.user_id
appy_feedback_status_history.changed_by → appy_managers.user_id
appy_feedback_comments.commenter_id → appy_managers.user_id
appy_managers.manager_id → appy_managers.user_id (self-referential)
```

#### Unique Managers Not in Employees (12 records)
| Name | Role | Email | User ID |
|------|------|-------|---------|
| Bob Wazneh | super-admin | <EMAIL> | user_2zgBSj0wjNqywixy4Btq5nsu5QU |
| Joelle Nemer | super-admin | <EMAIL> | user_30BrahPWzFVC7aRipcyPIKCcrT5 |
| CJN Automation | super-admin | <EMAIL> | user_2zb7hqgjUHn7tmpbVSgnmeWM167 |
| Romy Hemadeh | senior-manager | <EMAIL> | user_30Je6rRnHF3YJl3Jg3pbjj0ZndD |
| Tarek Hassoun | senior-manager | <EMAIL> | user_30YLxMbbJa4ZIuLhgHV3Hectxlw |
| Mario Nawfal | manager | <EMAIL> | mario_manager_id |
| Roundtable Bookings | manager | <EMAIL> | user_30MYFpb8B6gR3oyzlc7QaZfdBn9 |
| Events Roundtable | manager | <EMAIL> | user_30HKQx1zZVlR3skyZDuH4yGJXaP |
| Roundtable Team | manager | <EMAIL> | user_30HHRS7BZpwM3Tz679MP0vfcdqA |
| Sarah Adams | manager | <EMAIL> | user_30JJPvPEG2VJTL2A1bl8GkJMcJf |
| CJHenders | manager | <EMAIL> | user_30N8rwb8hUJwEZYoag4DnuxJAAD |
| Joe s | manager | <EMAIL> | user_30aungVeSEWDu1mITQFH0CL3L0Y |

#### Code Dependencies (22 files)
- Authentication: `lib/auth.ts`, `lib/admin-middleware.ts`
- Data Access: `lib/data/employees.ts`, `lib/db/domains/*.ts`
- Services: `lib/services/email-admin.ts`, `lib/services/notifications.ts`
- Scripts: `scripts/update-manager-clerk-ids.js`, `scripts/sync-clerk-ids.js`
- API Routes: `app/api/notifications/test/route.ts`
- Pages: `app/debug-user/page.tsx`

#### RLS Policies on appy_managers
1. "Accountants can read managers" - SELECT for accountants
2. "Admins can manage managers" - ALL operations for admins
3. "Managers can read own record" - SELECT own record

---

## 🏗️ MIGRATION ARCHITECTURE

### New appy_employees Schema
```sql
-- Existing columns remain unchanged
-- New columns to add:
clerk_user_id TEXT UNIQUE,              -- Clerk authentication ID
is_people_manager BOOLEAN DEFAULT FALSE, -- Can manage other employees
can_conduct_appraisals BOOLEAN DEFAULT FALSE, -- Can conduct appraisals
updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()

-- Indexes for performance
CREATE INDEX idx_appy_employees_clerk_user_id ON appy_employees(clerk_user_id);
CREATE INDEX idx_appy_employees_role_rank ON appy_employees(role_rank);
CREATE INDEX idx_appy_employees_is_people_manager ON appy_employees(is_people_manager);
```

### Data Model Comparison
| Feature | Current (Two Tables) | New (Single Table) |
|---------|---------------------|-------------------|
| Person ID | UUID (employees) + Clerk ID (managers) | UUID only |
| Auth ID | Stored in appy_managers.user_id | Stored in appy_employees.clerk_user_id |
| Role Hierarchy | appy_managers.role | appy_employees.role_rank |
| Manager Flag | Implicit (exists in managers table) | Explicit (is_people_manager) |
| Duplicates | 33 people in both tables | Zero duplicates |

---

## 📋 DETAILED MIGRATION PLAN

### PHASE 1: DATABASE PREPARATION (Day 1-2)

#### Step 1.1: Add New Columns to appy_employees
```sql
-- Add columns for Clerk authentication and management flags
ALTER TABLE appy_employees 
ADD COLUMN clerk_user_id TEXT UNIQUE,
ADD COLUMN is_people_manager BOOLEAN DEFAULT FALSE,
ADD COLUMN can_conduct_appraisals BOOLEAN DEFAULT FALSE,
ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create indexes for performance
CREATE INDEX idx_appy_employees_clerk_user_id ON appy_employees(clerk_user_id);
CREATE INDEX idx_appy_employees_role_rank ON appy_employees(role_rank);
CREATE INDEX idx_appy_employees_is_people_manager ON appy_employees(is_people_manager);

-- Add check constraint for role_rank consistency
ALTER TABLE appy_employees
ADD CONSTRAINT chk_manager_role_consistency 
CHECK (
  (role_rank IN ('manager', 'senior-manager', 'super-admin') AND is_people_manager = TRUE) 
  OR 
  (role_rank = 'employee')
);
```

#### Step 1.2: Create Audit Tables
```sql
-- Create audit table for tracking migration
CREATE TABLE appy_migration_audit (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  action TEXT NOT NULL,
  table_name TEXT NOT NULL,
  record_id TEXT,
  old_data JSONB,
  new_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by TEXT DEFAULT current_user
);

-- Create mapping table for ID translations
CREATE TABLE appy_manager_employee_mapping (
  manager_user_id TEXT PRIMARY KEY,
  employee_id UUID NOT NULL,
  full_name TEXT NOT NULL,
  migration_status TEXT DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  FOREIGN KEY (employee_id) REFERENCES appy_employees(id)
);
```

### PHASE 2: DATA MIGRATION (Day 3-4)

#### Step 2.1: Migrate Unique Managers to appy_employees
```sql
-- Insert managers that don't exist in employees table
INSERT INTO appy_employees (
  id,
  full_name,
  first_name,
  last_name,
  email,
  clerk_user_id,
  role_rank,
  is_people_manager,
  can_conduct_appraisals,
  department_id,
  manager_id,
  active,
  created_at
)
SELECT 
  gen_random_uuid() as id,
  m.full_name,
  SPLIT_PART(m.full_name, ' ', 1) as first_name,
  SUBSTRING(m.full_name FROM POSITION(' ' IN m.full_name) + 1) as last_name,
  m.email,
  m.user_id as clerk_user_id,
  m.role::text::appy_role_rank as role_rank,
  TRUE as is_people_manager,
  TRUE as can_conduct_appraisals,
  m.department_id,
  m.manager_id,
  m.active,
  m.created_at
FROM appy_managers m
WHERE NOT EXISTS (
  SELECT 1 FROM appy_employees e 
  WHERE e.full_name = m.full_name
)
AND m.active = TRUE;

-- Log the migration
INSERT INTO appy_migration_audit (action, table_name, record_id, new_data)
SELECT 
  'INSERT_UNIQUE_MANAGER',
  'appy_employees',
  m.user_id,
  jsonb_build_object('full_name', m.full_name, 'role', m.role)
FROM appy_managers m
WHERE NOT EXISTS (
  SELECT 1 FROM appy_employees e 
  WHERE e.full_name = m.full_name
);
```

#### Step 2.2: Update Existing Employees with Clerk IDs
```sql
-- Update employees that exist in both tables with their Clerk IDs
UPDATE appy_employees e
SET 
  clerk_user_id = m.user_id,
  is_people_manager = CASE 
    WHEN e.role_rank IN ('manager', 'senior-manager', 'super-admin') THEN TRUE 
    ELSE FALSE 
  END,
  can_conduct_appraisals = CASE 
    WHEN e.role_rank IN ('manager', 'senior-manager', 'super-admin') THEN TRUE 
    ELSE FALSE 
  END,
  updated_at = NOW()
FROM appy_managers m
WHERE e.full_name = m.full_name
  AND m.active = TRUE
  AND e.active = TRUE;

-- Create mapping for all managers
INSERT INTO appy_manager_employee_mapping (manager_user_id, employee_id, full_name, migration_status)
SELECT 
  m.user_id,
  e.id,
  m.full_name,
  'mapped'
FROM appy_managers m
JOIN appy_employees e ON m.full_name = e.full_name
WHERE m.active = TRUE;
```

#### Step 2.3: Create Temporary Foreign Key Translation Function
```sql
CREATE OR REPLACE FUNCTION get_employee_id_from_manager_id(p_manager_user_id TEXT)
RETURNS UUID AS $$
DECLARE
  v_employee_id UUID;
BEGIN
  -- First check mapping table
  SELECT employee_id INTO v_employee_id
  FROM appy_manager_employee_mapping
  WHERE manager_user_id = p_manager_user_id;
  
  IF v_employee_id IS NOT NULL THEN
    RETURN v_employee_id;
  END IF;
  
  -- Fallback to direct lookup
  SELECT id INTO v_employee_id
  FROM appy_employees
  WHERE clerk_user_id = p_manager_user_id;
  
  RETURN v_employee_id;
END;
$$ LANGUAGE plpgsql;
```

### PHASE 3: FOREIGN KEY MIGRATION (Day 5-6)

#### Step 3.1: Add New Foreign Key Columns
```sql
-- Add new UUID columns to reference appy_employees
ALTER TABLE appy_appraisals ADD COLUMN manager_employee_id UUID;
ALTER TABLE appy_employee_feedback ADD COLUMN reviewed_by_employee_id UUID;
ALTER TABLE appy_employee_feedback ADD COLUMN resolved_by_employee_id UUID;
ALTER TABLE appy_employee_managers ADD COLUMN manager_employee_id UUID;
ALTER TABLE appy_feedback_status_history ADD COLUMN changed_by_employee_id UUID;
ALTER TABLE appy_feedback_comments ADD COLUMN commenter_employee_id UUID;
```

#### Step 3.2: Populate New Foreign Key Columns
```sql
-- Update appy_appraisals
UPDATE appy_appraisals a
SET manager_employee_id = get_employee_id_from_manager_id(a.manager_id)
WHERE a.manager_id IS NOT NULL;

-- Update appy_employee_feedback
UPDATE appy_employee_feedback f
SET 
  reviewed_by_employee_id = get_employee_id_from_manager_id(f.reviewed_by),
  resolved_by_employee_id = get_employee_id_from_manager_id(f.resolved_by);

-- Update appy_employee_managers
UPDATE appy_employee_managers em
SET manager_employee_id = get_employee_id_from_manager_id(em.manager_id);

-- Update appy_feedback_status_history
UPDATE appy_feedback_status_history h
SET changed_by_employee_id = get_employee_id_from_manager_id(h.changed_by);

-- Update appy_feedback_comments
UPDATE appy_feedback_comments c
SET commenter_employee_id = get_employee_id_from_manager_id(c.commenter_id);
```

#### Step 3.3: Add Foreign Key Constraints
```sql
-- Add constraints to new columns
ALTER TABLE appy_appraisals 
ADD CONSTRAINT fk_appraisals_manager_employee 
FOREIGN KEY (manager_employee_id) REFERENCES appy_employees(id);

ALTER TABLE appy_employee_feedback 
ADD CONSTRAINT fk_feedback_reviewed_by_employee 
FOREIGN KEY (reviewed_by_employee_id) REFERENCES appy_employees(id);

ALTER TABLE appy_employee_feedback 
ADD CONSTRAINT fk_feedback_resolved_by_employee 
FOREIGN KEY (resolved_by_employee_id) REFERENCES appy_employees(id);

ALTER TABLE appy_employee_managers 
ADD CONSTRAINT fk_employee_managers_manager_employee 
FOREIGN KEY (manager_employee_id) REFERENCES appy_employees(id);

ALTER TABLE appy_feedback_status_history 
ADD CONSTRAINT fk_status_history_changed_by_employee 
FOREIGN KEY (changed_by_employee_id) REFERENCES appy_employees(id);

ALTER TABLE appy_feedback_comments 
ADD CONSTRAINT fk_comments_commenter_employee 
FOREIGN KEY (commenter_employee_id) REFERENCES appy_employees(id);
```

### PHASE 4: CODE MIGRATION (Day 7-10)

#### Step 4.1: Update Authentication Logic
```typescript
// lib/auth.ts - Update to use appy_employees.clerk_user_id
export async function getCurrentUser() {
  const { userId } = await auth()
  if (!userId) return null

  // Query appy_employees instead of appy_managers
  const { data: employee } = await supabaseAdmin
    .from('appy_employees')
    .select('*')
    .eq('clerk_user_id', userId)
    .single()

  if (!employee) return null

  return {
    id: employee.id,
    clerkId: employee.clerk_user_id,
    email: employee.email,
    fullName: employee.full_name,
    role: employee.role_rank,
    isManager: employee.is_people_manager,
    canConductAppraisals: employee.can_conduct_appraisals
  }
}
```

#### Step 4.2: Update Manager Queries
```typescript
// Replace all queries to appy_managers with appy_employees
// Example: lib/db/domains/managers.ts
export async function getManagers() {
  const { data, error } = await supabaseAdmin
    .from('appy_employees')
    .select('*')
    .eq('is_people_manager', true)
    .eq('active', true)
    .order('full_name')
  
  return { data, error }
}

export async function getManagerByClerkId(clerkId: string) {
  const { data, error } = await supabaseAdmin
    .from('appy_employees')
    .select('*')
    .eq('clerk_user_id', clerkId)
    .eq('is_people_manager', true)
    .single()
  
  return { data, error }
}
```

#### Step 4.3: Create Compatibility Views (Temporary)
```sql
-- Create view to simulate old appy_managers structure
CREATE OR REPLACE VIEW appy_managers_compat AS
SELECT 
  clerk_user_id as user_id,
  full_name,
  email,
  department_id,
  active,
  created_at,
  manager_id,
  role_rank::text as role
FROM appy_employees
WHERE is_people_manager = TRUE;

-- Grant same permissions as original table
GRANT SELECT ON appy_managers_compat TO PUBLIC;
```

### PHASE 5: TESTING & VALIDATION (Day 11-12)

#### Step 5.1: Data Integrity Checks
```sql
-- Verify all managers were migrated
SELECT 'Managers not migrated' as check_name, COUNT(*) as count
FROM appy_managers m
WHERE NOT EXISTS (
  SELECT 1 FROM appy_employees e 
  WHERE e.clerk_user_id = m.user_id OR e.full_name = m.full_name
);

-- Verify foreign key migrations
SELECT 'Unmapped appraisals' as check_name, COUNT(*) as count
FROM appy_appraisals
WHERE manager_id IS NOT NULL AND manager_employee_id IS NULL;

-- Check for duplicate Clerk IDs
SELECT clerk_user_id, COUNT(*) as count
FROM appy_employees
WHERE clerk_user_id IS NOT NULL
GROUP BY clerk_user_id
HAVING COUNT(*) > 1;
```

#### Step 5.2: Application Testing Checklist
- [ ] User login with Clerk authentication
- [ ] Manager dashboard access
- [ ] Employee hierarchy display
- [ ] Appraisal creation and assignment
- [ ] Feedback submission and review
- [ ] Admin functions (user management)
- [ ] Report generation
- [ ] Email notifications

### PHASE 6: CUTOVER & CLEANUP (Day 13-14)

#### Step 6.1: Switch to New Columns
```sql
-- Drop old foreign key constraints
ALTER TABLE appy_appraisals DROP CONSTRAINT IF EXISTS appy_appraisals_manager_id_fkey;
ALTER TABLE appy_employee_feedback DROP CONSTRAINT IF EXISTS appy_employee_feedback_reviewed_by_fkey;
ALTER TABLE appy_employee_feedback DROP CONSTRAINT IF EXISTS appy_employee_feedback_resolved_by_fkey;
ALTER TABLE appy_employee_managers DROP CONSTRAINT IF EXISTS appy_employee_managers_manager_id_fkey;
ALTER TABLE appy_feedback_status_history DROP CONSTRAINT IF EXISTS appy_feedback_status_history_changed_by_fkey;
ALTER TABLE appy_feedback_comments DROP CONSTRAINT IF EXISTS appy_feedback_comments_commenter_id_fkey;

-- Rename columns
ALTER TABLE appy_appraisals RENAME COLUMN manager_id TO manager_id_old;
ALTER TABLE appy_appraisals RENAME COLUMN manager_employee_id TO manager_id;

ALTER TABLE appy_employee_feedback RENAME COLUMN reviewed_by TO reviewed_by_old;
ALTER TABLE appy_employee_feedback RENAME COLUMN reviewed_by_employee_id TO reviewed_by;
ALTER TABLE appy_employee_feedback RENAME COLUMN resolved_by TO resolved_by_old;
ALTER TABLE appy_employee_feedback RENAME COLUMN resolved_by_employee_id TO resolved_by;

-- Continue for other tables...
```

#### Step 6.2: Archive appy_managers Table
```sql
-- Rename table instead of dropping (for rollback safety)
ALTER TABLE appy_managers RENAME TO appy_managers_archived_20250801;

-- Drop the compatibility view if no longer needed
DROP VIEW IF EXISTS appy_managers_compat;

-- Create a summary view for reference
CREATE VIEW appy_managers_archive_summary AS
SELECT 
  COUNT(*) as total_records,
  COUNT(DISTINCT user_id) as unique_users,
  MIN(created_at) as earliest_record,
  MAX(created_at) as latest_record
FROM appy_managers_archived_20250801;
```

### PHASE 7: POST-MIGRATION (Day 15+)

#### Step 7.1: Update RLS Policies
```sql
-- Create new RLS policies on appy_employees
ALTER TABLE appy_employees ENABLE ROW LEVEL SECURITY;

-- Policy: Users can read their own record
CREATE POLICY "Users can read own employee record" ON appy_employees
FOR SELECT USING (clerk_user_id = (auth.uid())::text);

-- Policy: Managers can read their team
CREATE POLICY "Managers can read their team" ON appy_employees
FOR SELECT USING (
  manager_id IN (
    SELECT id FROM appy_employees 
    WHERE clerk_user_id = (auth.uid())::text
  )
);

-- Policy: Admins can manage all employees
CREATE POLICY "Admins can manage all employees" ON appy_employees
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM appy_employees 
    WHERE clerk_user_id = (auth.uid())::text 
    AND role_rank IN ('super-admin', 'hr-admin')
  )
);
```

#### Step 7.2: Performance Optimization
```sql
-- Add composite indexes for common queries
CREATE INDEX idx_appy_employees_manager_active 
ON appy_employees(manager_id, active) 
WHERE active = TRUE;

CREATE INDEX idx_appy_employees_dept_role 
ON appy_employees(department_id, role_rank) 
WHERE active = TRUE;

-- Update table statistics
ANALYZE appy_employees;
```

---

## 🔄 ROLLBACK PLAN

### Immediate Rollback (Within 24 hours)
1. **Restore Foreign Keys**:
   ```sql
   -- Revert column renames
   ALTER TABLE appy_appraisals RENAME COLUMN manager_id TO manager_employee_id;
   ALTER TABLE appy_appraisals RENAME COLUMN manager_id_old TO manager_id;
   -- Continue for other tables...
   ```

2. **Restore appy_managers Table**:
   ```sql
   ALTER TABLE appy_managers_archived_20250801 RENAME TO appy_managers;
   ```

3. **Revert Code Changes**:
   - Git revert the migration commits
   - Redeploy previous version

### Extended Rollback (After 24 hours)
1. **Create Reverse Mapping**:
   ```sql
   -- Update appy_managers with any new data from appy_employees
   UPDATE appy_managers m
   SET email = e.email, active = e.active
   FROM appy_employees e
   WHERE m.user_id = e.clerk_user_id;
   ```

2. **Restore Application Functionality**:
   - Deploy compatibility layer
   - Gradually migrate back

---

## 🧪 TESTING STRATEGY

### Unit Tests
```typescript
describe('Employee Management', () => {
  test('getCurrentUser returns employee data', async () => {
    const user = await getCurrentUser()
    expect(user).toHaveProperty('id')
    expect(user).toHaveProperty('clerkId')
    expect(user).toHaveProperty('isManager')
  })

  test('getManagers returns only people managers', async () => {
    const { data: managers } = await getManagers()
    expect(managers.every(m => m.is_people_manager)).toBe(true)
  })
})
```

### Integration Tests
1. **Authentication Flow**:
   - Login as employee
   - Login as manager
   - Login as admin
   - Verify correct permissions

2. **Data Integrity**:
   - Create appraisal → Verify manager assignment
   - Submit feedback → Verify reviewer assignment
   - Check hierarchy → Verify no duplicates

### Performance Tests
```sql
-- Benchmark queries before and after migration
EXPLAIN ANALYZE
SELECT e.*, m.full_name as manager_name
FROM appy_employees e
LEFT JOIN appy_employees m ON e.manager_id = m.id
WHERE e.department_id = '...' AND e.active = TRUE;
```

---

## ⚠️ RISK ASSESSMENT

### High Risk Items
1. **Authentication Breakage**:
   - **Risk**: Users unable to login
   - **Mitigation**: Extensive testing, gradual rollout
   - **Rollback**: Immediate revert possible

2. **Data Loss**:
   - **Risk**: Foreign key mappings incorrect
   - **Mitigation**: Comprehensive backup, mapping audit trail
   - **Rollback**: Restore from backup

### Medium Risk Items
1. **Performance Degradation**:
   - **Risk**: Slower queries due to larger table
   - **Mitigation**: Proper indexing, query optimization
   - **Monitor**: Query performance metrics

2. **Permission Issues**:
   - **Risk**: RLS policies not working correctly
   - **Mitigation**: Thorough testing of all user roles
   - **Rollback**: Revert to old policies

### Low Risk Items
1. **UI Display Issues**:
   - **Risk**: Hierarchy display showing incorrect data
   - **Mitigation**: Already updated to use appy_employees
   - **Fix**: Frontend hotfix if needed

---

## 📅 IMPLEMENTATION TIMELINE

### Week 1: Preparation & Development
- **Day 1-2**: Database schema changes
- **Day 3-4**: Data migration scripts
- **Day 5-6**: Foreign key updates

### Week 2: Code Updates & Testing
- **Day 7-8**: Update authentication and queries
- **Day 9-10**: Update remaining code files
- **Day 11-12**: Comprehensive testing

### Week 3: Deployment & Monitoring
- **Day 13**: Production migration (off-hours)
- **Day 14**: Monitoring and hotfixes
- **Day 15+**: Performance optimization

---

## 📊 SUCCESS METRICS

### Immediate Success Criteria
- [ ] Zero duplicate employees in hierarchy
- [ ] All users can login successfully
- [ ] All existing features work correctly
- [ ] No data loss during migration

### Long-term Success Metrics
- [ ] 20% reduction in query complexity
- [ ] 15% improvement in page load times
- [ ] 50% reduction in data sync issues
- [ ] 100% data consistency

---

## 🛠️ MAINTENANCE CONSIDERATIONS

### Post-Migration Tasks
1. **Documentation Updates**:
   - Update API documentation
   - Update database schema docs
   - Update developer onboarding

2. **Monitoring Setup**:
   ```sql
   -- Create monitoring views
   CREATE VIEW employee_stats AS
   SELECT 
     COUNT(*) as total_employees,
     COUNT(DISTINCT clerk_user_id) as with_clerk_id,
     COUNT(*) FILTER (WHERE is_people_manager) as managers,
     COUNT(*) FILTER (WHERE role_rank = 'employee') as employees
   FROM appy_employees WHERE active = TRUE;
   ```

3. **Regular Audits**:
   - Weekly: Check for new duplicates
   - Monthly: Review performance metrics
   - Quarterly: Architecture review

---

## 📝 APPENDIX

### A. SQL Scripts Repository
All migration scripts will be stored in:
- `/scripts/migration/phase1_schema_changes.sql`
- `/scripts/migration/phase2_data_migration.sql`
- `/scripts/migration/phase3_foreign_keys.sql`
- `/scripts/migration/phase4_cleanup.sql`
- `/scripts/migration/rollback_emergency.sql`

### B. Code Change Checklist
- [ ] lib/auth.ts
- [ ] lib/admin-middleware.ts
- [ ] lib/data/employees.ts
- [ ] lib/db/domains/managers.ts
- [ ] lib/db/domains/appraisals.ts
- [ ] lib/db/domains/feedback.ts
- [ ] lib/db/domains/relationships.ts
- [ ] lib/services/email-admin.ts
- [ ] lib/services/notifications.ts
- [ ] All other affected files...

### C. Communication Plan
1. **Stakeholder Notification**: 2 weeks before
2. **User Communication**: 1 week before
3. **Maintenance Window**: 4 hours (Saturday 2 AM - 6 AM)
4. **Post-Migration Report**: Within 48 hours

---

**Document Version**: 1.0  
**Created**: August 1, 2025  
**Author**: System Architecture Team  
**Status**: READY FOR REVIEW