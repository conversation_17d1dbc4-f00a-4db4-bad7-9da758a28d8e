# Routes & Permissions Documentation

## Overview

This document provides a comprehensive guide to all routes in the Employee Appraisal Management System and their permission requirements. The application uses Clerk for authentication and implements role-based access control (RBAC) with six distinct user roles.

## User Roles & Hierarchy

The system defines six user roles with hierarchical permissions:

### 1. `super-admin`
- **Highest level access**
- Can access all routes and bypass most restrictions
- Full system administration capabilities
- Can approve all appraisals and manage all users

### 2. `hr-admin`
- **HR management access**
- Can manage employees, departments, and periods within their assigned department(s)
- Can approve appraisals for employees in their department hierarchy only
- Can approve appraisals for employees whose managers report to them
- Access to HR-specific features and reports

### 3. `senior-manager`
- **Department-level management**
- Can manage employees in their department hierarchy
- Can approve appraisals from direct report managers
- Limited administrative capabilities

### 4. `admin`
- **Administrative access**
- Read-only access to most administrative features
- Cannot modify critical system settings
- Limited approval capabilities

### 5. `manager`
- **Team management**
- Can create and submit appraisals for assigned employees
- Can view and manage their direct reports
- Basic team oversight capabilities

### 6. `accountant`
- **Financial/Payroll access**
- Read-only access to employee data for payment processing
- Can export payroll information
- Cannot modify employee or appraisal data

## Public Routes (No Authentication Required)

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/` | Landing page | Public |
| `/sign-in` | Clerk sign-in page | Public |
| `/sign-up` | Clerk sign-up page | Public |

## Authenticated Routes

### Dashboard Routes

#### Main Dashboard
| Route | Required Roles | Description | Additional Restrictions |
|-------|---------------|-------------|------------------------|
| `/dashboard` | All authenticated users | Main dashboard | Role-specific content |

#### Employee Management
| Route | Required Roles | Description | Additional Restrictions |
|-------|---------------|-------------|------------------------|
| `/dashboard/employees` | `super-admin`, `hr-admin`, `senior-manager`, `manager` | Employee list | Managers see only assigned employees |
| `/dashboard/employees/[id]/profile` | `super-admin`, `hr-admin`, `senior-manager`, `manager` | Employee profile | Must have access to specific employee |
| `/dashboard/employees/[id]/profile/edit` | `super-admin`, `hr-admin`, `senior-manager` | Edit employee | Cannot edit employees outside hierarchy |
| `/dashboard/employees/hierarchy` | `super-admin`, `hr-admin`, `senior-manager`, `manager` | Employee hierarchy view | Limited to accessible employees |
| `/dashboard/add-employee` | `super-admin`, `hr-admin` | Add new employee | Full employee creation |
| `/dashboard/add-people` | `super-admin`, `hr-admin` | Bulk add employees | Batch employee creation |

#### Department Management
| Route | Required Roles | Description | Additional Restrictions |
|-------|---------------|-------------|------------------------|
| `/dashboard/departments` | `super-admin`, `hr-admin`, `senior-manager` | Department management | Senior-managers see limited view |

#### Appraisal Management
| Route | Required Roles | Description | Additional Restrictions |
|-------|---------------|-------------|------------------------|
| `/dashboard/appraisal/[employeeId]` | `super-admin`, `hr-admin`, `senior-manager`, `manager` | Appraisal form | Must be assigned to employee |
| `/dashboard/periods` | `super-admin`, `hr-admin` | Appraisal periods | Full CRUD access |

#### Approval Workflows
| Route | Required Roles | Description | Additional Restrictions |
|-------|---------------|-------------|------------------------|
| `/dashboard/approvals` | `super-admin`, `hr-admin`, `senior-manager`, `manager`, `accountant` | Approval dashboard | Role-based approval levels |
| `/dashboard/approvals/multi-level` | `super-admin`, `hr-admin`, `senior-manager` | Multi-level approvals | Hierarchical approval chain |

#### Administrative Areas
| Route | Required Roles | Description | Additional Restrictions |
|-------|---------------|-------------|------------------------|
| `/dashboard/admin` | `super-admin`, `admin` | Admin redirect | Redirects to user-specific admin page |
| `/dashboard/admin/[adminId]` | `super-admin`, `admin` | User admin dashboard | Protected by admin middleware |
| `/dashboard/admin/email-settings` | `super-admin` | Email configuration | Super-admin only |

#### Specialized Access
| Route | Required Roles | Description | Additional Restrictions |
|-------|---------------|-------------|------------------------|
| `/dashboard/accounting` | `accountant`, `hr-admin`, `super-admin` | Accounting dashboard | Payment processing view |
| `/dashboard/team` | `super-admin`, `manager`, `senior-manager`, `hr-admin` | Team overview | Manager hierarchy applies |
| `/dashboard/feedback` | `super-admin`, `hr-admin`, `senior-manager`, `manager` | Feedback management | Role-based visibility |
| `/dashboard/hr/feedback` | `hr-admin`, `super-admin` | HR feedback management | HR-specific features |
| `/dashboard/pto` | All authenticated users | PTO management | Role-based approval rights |

#### Profile Management
| Route | Required Roles | Description | Additional Restrictions |
|-------|---------------|-------------|------------------------|
| `/profile/[id]` | All authenticated users | User profile view | Can view own profile + managed employees |

#### Debug Routes (Development)
| Route | Required Roles | Description | Additional Restrictions |
|-------|---------------|-------------|------------------------|
| `/debug-auth` | All authenticated users | Auth debugging | Development only |
| `/debug-user` | All authenticated users | User debugging | Development only |

## API Routes

### Authentication & User Management
| Route | Method | Required Roles | Description |
|-------|---------|---------------|-------------|
| `/api/employees/[id]/managers` | GET/POST | `super-admin`, `hr-admin` | Employee manager assignment |
| `/api/test-employees` | GET | `super-admin`, `hr-admin` | Employee testing endpoint |

### Administrative APIs
| Route | Method | Required Roles | Description |
|-------|---------|---------------|-------------|
| `/api/admin/email-settings` | GET/POST/PUT | `super-admin` | Email system configuration |

### Approval Workflows
| Route | Method | Required Roles | Description |
|-------|---------|---------------|-------------|
| `/api/approvals/pending` | GET | `manager`, `senior-manager`, `hr-admin`, `admin`, `super-admin` | Pending approvals for user |
| `/api/approvals/process` | POST | `senior-manager`, `hr-admin`, `super-admin` | Process approval decisions |

### Feedback System
| Route | Method | Required Roles | Description |
|-------|---------|---------------|-------------|
| `/api/feedback/[id]` | GET/PUT/DELETE | `super-admin`, `hr-admin`, `senior-manager`, `manager` | Individual feedback management |
| `/api/feedback/submit` | POST | All authenticated users | Submit feedback |
| `/api/feedback/list` | GET | `super-admin`, `hr-admin`, `senior-manager`, `manager` | List feedback items |
| `/api/feedback/stats` | GET | `super-admin`, `hr-admin` | Feedback statistics |
| `/api/feedback/hr` | GET | `hr-admin`, `super-admin` | HR feedback overview |

### Notification System
| Route | Method | Required Roles | Description |
|-------|---------|---------------|-------------|
| `/api/notifications/preferences` | GET/POST | All authenticated users | User notification settings |
| `/api/notifications/test` | POST | `super-admin` | Test notification system |

### System Maintenance
| Route | Method | Required Roles | Description |
|-------|---------|---------------|-------------|
| `/api/cron/notifications` | POST | System/Cron jobs | Automated notification processing |

## Special Permission Rules

### Admin Middleware Protection
Routes matching `/dashboard/admin/[adminId]` are protected by special admin middleware that:
1. Requires `admin` or `super-admin` role
2. Validates user can access the specific admin page
3. Uses `canAccessAdminPage()` function for additional validation

### Employee Access Hierarchy
For employee-related routes, access is determined by:
1. **Super-admin & HR-admin**: Can access all employees
2. **Senior-manager**: Can access employees in their department hierarchy
3. **Manager**: Can access only directly assigned employees
4. **Accountant**: Read-only access to employee data for payment processing

### Appraisal Approval Chain
The approval system follows a hierarchical structure:
1. **Managers** submit appraisals for their assigned employees
2. **Senior-managers** can approve manager submissions within their department hierarchy
3. **HR-admin** can approve submissions for employees in their assigned departments or whose managers report to them
4. **Super-admin** can approve all submissions and bypass all restrictions

**Important**: HR admins can only approve appraisals for employees they have departmental authority over, not all employees company-wide.

### Department Restrictions
Many routes implement department-based restrictions:
- Managers can only see employees in their assigned departments
- Senior-managers have broader department access within their hierarchy
- HR-admins can access employees within their department hierarchy
- Only Super-admins bypass all department restrictions

## Security Considerations

### Recent Security Fix - HR Admin Permissions
**Important**: As of the latest update, HR admin permissions have been restricted for security:

**Previous (Insecure) Behavior:**
- HR admins could approve ANY appraisal submitted by ANY manager/senior-manager
- HR admins had blanket access to ALL employees company-wide
- No departmental or hierarchical restrictions applied

**Current (Secure) Behavior:**
- HR admins can only approve appraisals for employees in their assigned department(s)
- HR admins can only approve appraisals for employees whose managers report to them
- HR admins can only access employee data within their department hierarchy
- Global access is reserved for super-admins only

**Functions Updated:**
- `canApproveAppraisal()` - Now denies HR admin requests without employee context
- `canApproveEmployeeAppraisal()` - Implements department hierarchy checking for HR admins
- `canAccessEmployee()` - Restricts HR admin access to department hierarchy

### Rate Limiting
The `checkRateLimit()` function provides protection against:
- Rapid successive requests from the same user
- Configurable limits per action type
- Default: 10 requests per minute window

### Audit Logging
All user actions are logged via `logUserAction()` including:
- User ID and role
- Action performed
- Timestamp and IP address
- User agent information

### Input Validation
All routes implement Zod schema validation for:
- Data sanitization (XSS protection)
- Type safety
- Business rule enforcement
- Required field validation

## Permission Matrix Quick Reference

| Route Category | super-admin | hr-admin | senior-manager | admin | manager | accountant |
|---------------|-------------|----------|----------------|-------|---------|------------|
| Employee Management | ✅ Full | ✅ Department Hierarchy | ✅ Limited | ❌ | ✅ Assigned Only | ✅ Read-Only |
| Department Management | ✅ Full | ✅ Assigned Departments | ✅ Limited | ❌ | ❌ | ❌ |
| Appraisal Creation | ✅ All | ✅ Department Hierarchy | ✅ Hierarchy | ❌ | ✅ Assigned | ❌ |
| Appraisal Approval | ✅ All | ✅ Department Hierarchy | ✅ Manager Reports | ❌ | ❌ | ❌ |
| Admin Settings | ✅ Full | ❌ | ❌ | ✅ Read-Only | ❌ | ❌ |
| Accounting/Payroll | ✅ Full | ✅ Department Hierarchy | ❌ | ❌ | ❌ | ✅ Full |
| Team Management | ✅ All | ✅ Department Hierarchy | ✅ Department | ❌ | ✅ Direct Reports | ❌ |
| Feedback System | ✅ All | ✅ Department Hierarchy | ✅ Department | ❌ | ✅ Team | ❌ |

## Error Handling

### Common HTTP Status Codes
- **401 Unauthorized**: User not authenticated
- **403 Forbidden**: User lacks required role/permission
- **404 Not Found**: Resource doesn't exist or user can't access it
- **500 Internal Server Error**: System error

### Redirect Behavior
- Unauthenticated users → `/sign-in`
- Insufficient permissions → `/dashboard?error=insufficient_permissions`
- Access denied → `/dashboard?error=access_denied`

---

*This documentation is current as of the latest system update. For questions about specific permission requirements, consult the source code in `/lib/auth.ts` and individual route handlers.*