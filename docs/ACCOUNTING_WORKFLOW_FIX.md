# Accounting Workflow Fix: Multi-Level Approval Integration

## Problem Statement

Previously, the accounting system would show appraisals as "ready-to-pay" immediately when a manager submitted an appraisal with that payment status, even if the multi-level approval workflow was still in progress. This meant that appraisals could appear ready for payment processing before senior manager approval was completed.

## Solution Overview

Modified the accounting data logic to only show "ready-to-pay" status when **both** conditions are met:
1. Manager originally marked the appraisal as "ready-to-pay" during submission
2. All approval workflow steps are completed (workflow status = "completed")

## Technical Implementation

### Files Modified

1. **`lib/data/accounting.ts`** - Main accounting data function
   - Added approval workflow status checking
   - Modified status mapping logic
   - Added comprehensive logging for debugging

### Key Changes

#### Before (Problematic Behavior)
```typescript
// Old logic - only checked payment_status
if (appraisal.payment_status) {
  status = appraisal.payment_status  // Would show "ready-to-pay" immediately
  submittedAt = appraisal.submitted_at
}
```

#### After (Fixed Behavior)
```typescript
// New logic - checks both payment_status AND workflow completion
const workflow = workflowMap.get(appraisal.id)
const isWorkflowCompleted = workflow?.status === 'completed'

if (appraisal.payment_status === 'ready-to-pay' && isWorkflowCompleted) {
  status = 'ready-to-pay'  // Only shows ready-to-pay when workflow is completed
  submittedAt = appraisal.submitted_at
} else if (appraisal.payment_status === 'contact-manager' && isWorkflowCompleted) {
  status = 'contact-manager'
  submittedAt = appraisal.submitted_at
} else if (appraisal.status === 'submitted') {
  status = 'submitted'  // Shows as submitted while workflow is in progress
  submittedAt = appraisal.submitted_at
}
```

## Status Flow Examples

### Example 1: Manager says "Ready to Pay" but Senior Manager hasn't approved yet
- **Manager Action**: Submits appraisal with payment_status = "ready-to-pay"
- **Workflow Status**: "in_progress" (waiting for senior manager)
- **Accounting Display**: "submitted" (not ready for payment yet)
- **Result**: ✅ Accounting waits for senior manager approval

### Example 2: Manager says "Contact Manager" and Senior Manager approves
- **Manager Action**: Submits appraisal with payment_status = "contact-manager"  
- **Workflow Status**: "completed" (senior manager approved)
- **Accounting Display**: "contact-manager"
- **Result**: ✅ Accounting sees the manager's recommendation

### Example 3: Manager says "Ready to Pay" and Senior Manager approves
- **Manager Action**: Submits appraisal with payment_status = "ready-to-pay"
- **Workflow Status**: "completed" (senior manager approved)
- **Accounting Display**: "ready-to-pay"
- **Result**: ✅ Accounting can process payment

## Testing

Created test script `test/test-accounting-workflow.js` that verifies:
- ✅ Appraisals with payment_status="ready-to-pay" but incomplete workflow show as "submitted"
- ✅ Appraisals with payment_status="contact-manager" and completed workflow show as "contact-manager"  
- ✅ Appraisals with payment_status="ready-to-pay" and completed workflow show as "ready-to-pay"

## Debugging Features

Added comprehensive console logging:
```typescript
console.log(`🔍 [ACCOUNTING] Employee ${emp.fullName} - Appraisal ${appraisal.id}:`)
console.log(`  - Appraisal Status: ${appraisal.status}`)
console.log(`  - Payment Status: ${appraisal.payment_status}`)
console.log(`  - Workflow Status: ${workflow?.status || 'none'}`)
console.log(`  - Workflow Completed: ${isWorkflowCompleted}`)
```

## Database Considerations

The `getAppraisalsForAccountingReview()` function uses an RPC `get_appraisals_for_accounting_review` that may need similar updates at the database level for complete consistency.

## Benefits

1. **Prevents Premature Payment Processing**: Accounting can't accidentally process payments before all approvals are complete
2. **Maintains Manager Intent**: Still respects the manager's original payment recommendation
3. **Clear Audit Trail**: Comprehensive logging shows exactly why each status was assigned
4. **Backward Compatible**: Doesn't break existing functionality for appraisals without workflows

## Future Enhancements

1. Update database RPC functions to apply same logic
2. Add UI indicators showing approval progress
3. Consider email notifications when workflows complete and become ready for accounting
