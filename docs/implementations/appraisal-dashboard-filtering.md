# Appraisal Dashboard Filtering Implementation

## Problem Description

### Issue
- After appraisals are approved, they continue to show up in the manager dashboard
- This creates confusion as managers see completed appraisals mixed with active ones
- No way to filter appraisals by different appraisal periods

### Root Cause
In `lib/data/appraisals.ts`, the `getManagerAppraisals()` function only maps database statuses to UI statuses but doesn't filter out completed appraisals:

```typescript
// Current logic (lines 102-115)
if (appraisal.status === 'submitted') {
  status = 'submitted'
} else if (appraisal.status === 'pending') {
  status = 'draft'
}
// Missing: exclude 'approved', 'ready-to-pay', 'contact-manager' statuses
```

## Solution Design

### 1. Status Filtering Logic

**Current Status Flow:**
```
not-started → draft → submitted → approved → ready-to-pay/contact-manager
```

**New Filtering:**
- **Show in Active Dashboard**: `not-started`, `draft`, `submitted`, `rejected`, `senior-needed`
- **Hide from Active Dashboard**: `approved`, `ready-to-pay`, `contact-manager`

### 2. Period Filter Feature

**Implementation:**
- Add period selector dropdown to dashboard
- Allow users to view historical appraisal data
- Default to current active period
- Store selected period in component state

### 3. Database Query Updates

**Modified Function Signature:**
```typescript
export async function getManagerAppraisals(periodId?: string): Promise<EmployeeAppraisal[]>
```

**Filtering Logic:**
```typescript
// Only show active/pending appraisals for current period
const activeStatuses = ['not-started', 'draft', 'submitted', 'rejected', 'senior-needed']
return appraisals.filter(appraisal => {
  if (periodId && periodId !== currentPeriod.id) {
    // For historical periods, show all statuses
    return true
  }
  // For current period, only show active statuses
  return activeStatuses.includes(appraisal.status)
})
```

## Implementation Files

### 1. Data Layer Changes
**File**: `lib/data/appraisals.ts`
- Add optional `periodId` parameter to `getManagerAppraisals()`
- Filter out completed appraisals for active period
- Allow all statuses for historical periods

### 2. Dashboard Component Updates
**File**: `app/dashboard/page.tsx`
- Add period selection state
- Pass selected period to data fetching
- Update data fetching logic

### 3. Table Component Updates
**File**: `components/appraisal-dashboard-table.tsx`
- Add period filter dropdown to existing filters
- Style consistently with current filters
- Handle period change events

## Expected Behavior Changes

### Before Fix
- ✗ Approved appraisals show in active dashboard
- ✗ No way to view historical appraisals
- ✗ Confusing mix of active and completed work

### After Fix
- ✅ Only active appraisals show in current period dashboard
- ✅ Period filter allows historical data access
- ✅ Clear separation between active and completed work
- ✅ Better user experience and workflow clarity

## Testing Scenarios

1. **Active Period Dashboard**
   - Should only show: not-started, draft, submitted, rejected, senior-needed
   - Should hide: approved, ready-to-pay, contact-manager

2. **Historical Period View**
   - Should show all appraisals regardless of status
   - Useful for reviewing past performance data

3. **Period Switching**
   - Smooth transition between periods
   - Proper loading states during data fetch
   - Consistent filter behavior

## Migration Notes

- **Backward Compatibility**: Existing API calls without `periodId` will default to current period behavior
- **Performance**: Filtering is done in-memory, minimal database impact
- **UX**: New period filter integrates seamlessly with existing filter system