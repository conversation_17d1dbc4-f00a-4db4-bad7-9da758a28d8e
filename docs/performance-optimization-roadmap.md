# Performance Optimization Roadmap

This document outlines the planned performance optimizations for the Employee Appraisal Management System.

## ✅ **Phase 1: Enhanced Application Caching (COMPLETED)**

**Status**: Implemented
**Impact**: High (Reduces database queries by 70-80%)
**Effort**: Low

### What was implemented:
- Enhanced cache system with different TTLs for different data types
- Intelligent cache invalidation by type and pattern matching
- Cache statistics and monitoring via `/api/cache-stats`
- Batch manager role detection (replaced sequential queries)

### Cache Durations:
```typescript
EMPLOYEE_DATA: 30 minutes     // Stable data
MANAGER_ROLES: 1 hour         // Very stable
DEPARTMENTS: 1 hour           // Very stable
PERIODS: 24 hours             // Seasonal
PERFORMANCE_STATS: 2 minutes  // Dynamic data
APPRAISALS: 1 minute          // Frequent updates
```

### Files Modified:
- `lib/cache-enhanced.ts` (NEW)
- `lib/cache.ts` (Updated for backward compatibility)
- `lib/data/employees.ts` (Added caching to key functions)
- `lib/data/performance-stats.ts` (Updated cache TTLs)
- `app/api/cache-stats/route.ts` (NEW - monitoring endpoint)

---

## 🚀 **Phase 2: Database Query Optimization (PLANNED)**

**Status**: Planned for Sprint 2
**Impact**: Very High (Single query vs 4-5 queries)
**Effort**: Medium

### Current Problem:
Dashboard loading requires multiple sequential database queries:
1. `getEmployees()` - 157 rows
2. `getBatchManagerRoles()` - 2 queries with IN clauses
3. `getAppraisals()` - Join with period data
4. `getDepartments()` - Reference data
5. `getPerformanceStats()` - Aggregation queries

### Proposed Solution:

#### A. Single Comprehensive Dashboard Query
Create a database function that returns all dashboard data in one call:

```sql
-- File: migrations/add_dashboard_function.sql
CREATE OR REPLACE FUNCTION get_manager_dashboard_data(
    manager_user_id TEXT,
    period_id UUID DEFAULT NULL
)
RETURNS TABLE (
    -- Employee info
    employee_id UUID,
    employee_name TEXT,
    employee_email TEXT,
    department_id UUID,
    department_name TEXT,
    employee_active BOOLEAN,
    
    -- Manager info
    manager_role TEXT,
    is_manager BOOLEAN,
    manager_name TEXT,
    
    -- Appraisal info
    appraisal_id UUID,
    appraisal_status TEXT,
    performance_rating TEXT,
    submitted_at TIMESTAMPTZ,
    
    -- Hierarchy info
    hierarchy_level INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.id,
        e.full_name,
        e.email,
        e.department_id,
        d.name as dept_name,
        e.active,
        COALESCE(m.role, 'employee') as mgr_role,
        (m.role IS NOT NULL) as is_mgr,
        COALESCE(mgr_emp.full_name, 'No Manager') as mgr_name,
        a.id as appraisal_id,
        COALESCE(a.status, 'not-started') as status,
        a.question_1,
        a.submitted_at,
        COALESCE(h.hierarchy_level, 0) as h_level
    FROM appy_employees e
    LEFT JOIN appy_departments d ON e.department_id = d.id
    LEFT JOIN appy_managers m ON (e.id = m.user_id OR e.full_name = m.full_name) AND m.active = true
    LEFT JOIN appy_employees mgr_emp ON e.manager_id = mgr_emp.id
    LEFT JOIN appy_appraisals a ON e.id = a.employee_id 
        AND a.period_id = COALESCE(
            period_id, 
            (SELECT id FROM appy_periods WHERE closed = false LIMIT 1)
        )
    LEFT JOIN LATERAL (
        SELECT hierarchy_level FROM get_hierarchical_employees(manager_user_id) he 
        WHERE he.id = e.id
    ) h ON true
    WHERE 
        -- Super admin sees all employees
        (SELECT role FROM appy_managers WHERE user_id = manager_user_id AND active = true) = 'super-admin'
        OR
        -- Regular managers see their hierarchy
        e.id IN (
            SELECT employee_id FROM get_hierarchical_employees(manager_user_id)
        )
    ORDER BY e.full_name;
END;
$$ LANGUAGE plpgsql;
```

#### B. Implementation Changes

**File: `lib/db/index.ts`**
```typescript
export async function getManagerDashboardData(managerId: string, periodId?: string) {
    const { data, error } = await supabase.rpc('get_manager_dashboard_data', {
        manager_user_id: managerId,
        period_id: periodId
    })
    
    if (error) {
        console.error('Dashboard query error:', error)
        throw error
    }
    
    return data
}
```

**File: `lib/data/dashboard.ts` (NEW)**
```typescript
export async function getDashboardData(): Promise<DashboardData> {
    const currentUser = await getCurrentUser()
    if (!currentUser) throw new Error('Not authenticated')

    // Check cache first
    const cacheKey = `dashboard-${currentUser.id}`
    const cached = cache.get<DashboardData>(cacheKey)
    if (cached) return cached

    // Single database call instead of multiple
    const rawData = await db.getManagerDashboardData(currentUser.id)
    
    // Transform data for UI
    const dashboardData: DashboardData = {
        employees: rawData.map(row => ({
            id: row.employee_id,
            fullName: row.employee_name,
            departmentName: row.department_name,
            active: row.employee_active,
            managerRole: row.manager_role,
            isManager: row.is_manager
        })),
        appraisals: rawData.map(row => ({
            employeeId: row.employee_id,
            status: row.appraisal_status,
            rating: row.performance_rating,
            submittedAt: row.submitted_at
        })),
        performanceStats: calculateStatsFromRawData(rawData)
    }
    
    // Cache for 3 minutes
    cache.set(cacheKey, dashboardData, CACHE_DURATIONS.DASHBOARD_DATA)
    
    return dashboardData
}
```

#### C. Expected Impact
- **Query Count**: 5+ queries → 1 query
- **Performance**: 80-90% reduction in dashboard load time
- **Database Load**: Significant reduction in connection overhead
- **Consistency**: All data from single transaction

---

## ⚡ **Phase 3: Parallel Loading Pattern (PLANNED)**

**Status**: Planned for Sprint 2  
**Impact**: High (40-60% faster perceived performance)
**Effort**: Low

### Current Problem:
Dashboard components load sequentially, causing waterfall delays:

```typescript
// Current: Sequential loading
const appraisalsData = await getManagerAppraisals()      // Wait 500ms
const statsData = await getManagerPerformanceStats()    // Wait 300ms  
const teamData = await getEmployeesForManager()         // Wait 400ms
const managersData = await getManagers()                // Wait 200ms
// Total: 1400ms
```

### Proposed Solution:

#### A. Parallel Data Loading

**File: `app/dashboard/page.tsx`**
```typescript
export default async function ManagerDashboardPage() {
  const currentUser = await getCurrentUser()
  if (!currentUser) redirect('/sign-in')

  // Load all independent data in parallel
  const [
    appraisalsResult,
    statsResult, 
    teamResult,
    managersResult
  ] = await Promise.allSettled([
    getManagerAppraisals(),
    getManagerPerformanceStats(),
    getEmployeesForManager(currentUser.id, currentUser.role),
    getManagers()
  ])

  // Handle results gracefully (no single point of failure)
  const appraisals = appraisalsResult.status === 'fulfilled' 
    ? appraisalsResult.value 
    : []
    
  const performanceStats = statsResult.status === 'fulfilled'
    ? statsResult.value
    : getDefaultPerformanceStats()
    
  const teamMembers = teamResult.status === 'fulfilled'
    ? teamResult.value
    : []
    
  const managers = managersResult.status === 'fulfilled'
    ? managersResult.value
    : []

  // Log any failures for monitoring
  if (appraisalsResult.status === 'rejected') {
    console.error('Failed to load appraisals:', appraisalsResult.reason)
  }
  // ... similar for other failures

  return (
    <div className="dashboard">
      {/* Render with graceful degradation */}
    </div>
  )
}
```

#### B. Progressive Loading for Heavy Components

**File: `components/dashboard-progressive.tsx` (NEW)**
```typescript
'use client'

import { Suspense } from 'react'
import { ErrorBoundary } from './error-boundary'

export function ProgressiveDashboard({ initialData }: { initialData: any }) {
  return (
    <div className="dashboard">
      {/* Critical data loads immediately */}
      <div className="dashboard-header">
        <h1>Manager Dashboard</h1>
        <UserInfo user={initialData.user} />
      </div>

      {/* Performance overview loads first */}
      <ErrorBoundary fallback={<StatsSkeletonError />}>
        <Suspense fallback={<StatsSkeleton />}>
          <PerformanceOverview stats={initialData.stats} />
        </Suspense>
      </ErrorBoundary>

      {/* Team data loads in background */}
      <ErrorBoundary fallback={<TeamSkeletonError />}>
        <Suspense fallback={<TeamSkeleton />}>
          <TeamSection 
            employees={initialData.employees} 
            appraisals={initialData.appraisals} 
          />
        </Suspense>
      </ErrorBoundary>

      {/* Nice-to-have data loads last */}
      <ErrorBoundary fallback={<ChartsSkeletonError />}>
        <Suspense fallback={<ChartsSkeleton />}>
          <PerformanceCharts managerId={initialData.user.id} />
        </Suspense>
      </ErrorBoundary>
    </div>
  )
}
```

#### C. Smart Prefetching

**File: `lib/prefetch.ts` (NEW)**
```typescript
// Prefetch likely-needed data in the background
export class DataPrefetcher {
  private static prefetchQueue = new Set<string>()

  static async prefetchManagerData(managerId: string) {
    if (this.prefetchQueue.has(managerId)) return
    this.prefetchQueue.add(managerId)

    // Prefetch in background without blocking
    setTimeout(async () => {
      try {
        await Promise.all([
          getEmployeesForManager(managerId, 'manager'),
          getManagerPerformanceStats(managerId),
          getDepartments() // Likely to be needed
        ])
        console.log(`📦 Prefetched data for manager: ${managerId}`)
      } catch (error) {
        console.warn('Prefetch failed:', error)
      } finally {
        this.prefetchQueue.delete(managerId)
      }
    }, 100) // Small delay to not interfere with critical rendering
  }
}

// Usage in layout or navigation
export function DashboardLayout({ children }: { children: ReactNode }) {
  const { user } = useUser()
  
  useEffect(() => {
    if (user?.role === 'manager') {
      DataPrefetcher.prefetchManagerData(user.id)
    }
  }, [user])

  return <div>{children}</div>
}
```

#### D. Expected Impact
- **Perceived Load Time**: 40-60% faster
- **First Contentful Paint**: 200-300ms improvement  
- **User Experience**: Progressive enhancement instead of loading spinners
- **Resilience**: Single component failures don't break entire dashboard

---

## 📊 **Monitoring & Metrics**

### Cache Performance Monitoring
Access cache statistics at: `GET /api/cache-stats`

```json
{
  "stats": {
    "hitRate": "85%",
    "totalRequests": 1250,
    "cacheSize": 42,
    "entryTypes": {
      "employees": 12,
      "manager-roles": 8, 
      "performance-stats": 6,
      "departments": 4
    }
  }
}
```

### Performance Benchmarks (Target)
- **Dashboard Load Time**: < 800ms (from ~3000ms)
- **Cache Hit Rate**: > 80%
- **Database Queries per Request**: < 2 (from 5-7)
- **Memory Usage**: < 50MB cache size

---

## 🗓️ **Implementation Timeline**

### Week 1 ✅
- [x] Enhanced application caching
- [x] Batch manager role queries
- [x] Cache monitoring endpoint

### Week 2-3 (Phase 2)
- [ ] Database query optimization
- [ ] Single dashboard query function
- [ ] Parallel loading implementation

### Week 4 (Phase 3)  
- [ ] Progressive loading components
- [ ] Smart prefetching
- [ ] Performance monitoring dashboard

### Month 2+
- [ ] Redis integration for production
- [ ] Materialized views for complex queries
- [ ] Advanced performance analytics

---

## 🔧 **Quick Reference**

### Enable Debug Logging
```bash
export NODE_ENV=development
# Logs show cache hits/misses and performance metrics
```

### Clear Cache
```bash
curl -X DELETE http://localhost:3000/api/cache-stats
```

### Check Cache Stats
```bash
curl http://localhost:3000/api/cache-stats
```

### Cache Invalidation in Code
```typescript
import { CacheEventManager } from '@/lib/cache-enhanced'

// When employee data changes
CacheEventManager.onEmployeeUpdate(employeeId)

// When appraisal is submitted
CacheEventManager.onAppraisalUpdate(employeeId)
```