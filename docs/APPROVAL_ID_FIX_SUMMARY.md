# Multi-Level Approval ID Mismatch Fix

## Problem Summary
<PERSON> (senior manager) couldn't see <PERSON>'s (manager) appraisal approval in her multi-level approval dashboard due to user ID mismatches between Clerk authentication and database records.

## Root Cause
1. **Approval workflows** were created with old database user IDs (e.g., `user_2zgIPJfN1J3mcwjHH27wFQT9NQW`)
2. **Authentication system** returns Clerk IDs which don't match the database IDs
3. **Pending approvals lookup** only checked for direct ID matches, missing email-based fallback

## Solution Implemented

### 1. Enhanced Database Function
- **Updated**: `get_pending_approvals_for_user(p_user_id TEXT, p_user_email TEXT DEFAULT NULL)`
- **Added**: Email-based fallback lookup for ID mismatches
- **Backwards Compatible**: Still works with direct ID matches

### 2. TypeScript Updates
- **Updated**: `getPendingApprovalsForUser()` to accept optional email parameter
- **Modified**: All callers to pass both user ID and email
- **Files Changed**: 
  - `lib/data/approvals.ts`
  - `app/dashboard/approvals/multi-level/page.tsx`
  - `app/api/approvals/pending/route.ts`

### 3. Migration Scripts
- **Created**: `scripts/fix-pending-approval-ids.sql` - Updates existing pending approvals
- **Created**: `scripts/validate-approval-fix.sql` - Validates the fix works correctly

## Test Results
✅ **Mona can now see Mia's pending approval** using email lookup
✅ **Direct ID lookup still works** for backwards compatibility  
✅ **No data corruption** - all existing approvals remain intact
✅ **Audit trail maintained** - all changes logged in approval history

## Validation
```sql
-- Test shows Mona can see pending approval with email lookup
SELECT * FROM get_pending_approvals_for_user(
  'fake_clerk_id', 
  '<EMAIL>'
);
-- Result: Returns Mariana Ordonez's pending approval from Mia
```

## Production Deployment
1. ✅ Database function updated (backwards compatible)
2. ✅ TypeScript code updated  
3. ✅ Tested locally with development server
4. 🔄 **Next**: Deploy to production
5. 🔄 **Next**: Run migration script for existing approvals (optional)

## Impact
- **Zero downtime** - All changes are backwards compatible
- **Immediate fix** - Resolves ID mismatch issues for all users
- **Future-proof** - Handles both old and new ID formats
- **Auditable** - Full logging of all approval actions

## Manager Hierarchy Confirmed
- **Bob Wazneh** (Super Admin) → **Mona Bourgess** (Senior Manager) → **Mia Owaini** (Manager)
- **Approval Flow**: Mia submits appraisal → Mona approves → Complete
- **Email Lookup**: `<EMAIL>` → `user_2zgIPJfN1J3mcwjHH27wFQT9NQW`