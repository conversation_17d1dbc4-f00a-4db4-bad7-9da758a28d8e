import type { PerformanceStats } from '../types'
import { db } from '../db'
import { getEmployees, getEmployeesForManager } from './employees'
import { getPeriods } from './periods'
import { cache, CACHE_DURATIONS } from '../cache'

export async function getPerformanceStats(): Promise<PerformanceStats> {
  try {
    // Check cache first
    const cacheKey = 'global-performance-stats'
    const cachedStats = cache.get<PerformanceStats>(cacheKey)
    if (cachedStats) {
      // console.log('📊 Using cached performance stats')
      return cachedStats
    }

    // Get the current active period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)
    
    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      const emptyStats = {
        total: 0,
        belowExpectations: 0,
        meetsExpectations: 0,
        exceedsExpectations: 0,
        notStarted: 0,
        submittedCount: 0,
        draftCount: 0,
      }
      // Cache empty stats for a shorter time
      cache.set(cacheKey, emptyStats, 30000) // 30 seconds
      return emptyStats
    }

    // Get all appraisals for the current period
    const appraisals = await db.getPerformanceStatsByPeriod(currentPeriod.id)
    
    // Initialize counters
    let belowExpectations = 0
    let meetsExpectations = 0
    let exceedsExpectations = 0
    let submittedCount = 0
    let draftCount = 0
    
    // Count appraisals by q1 response and status
    appraisals.forEach(appraisal => {
      if (appraisal.status === 'submitted') {
        submittedCount++
        
        // Only count q1 responses for submitted appraisals
        if (appraisal.question_1 === 'below-expectations') {
          belowExpectations++
        } else if (appraisal.question_1 === 'meets-expectations') {
          meetsExpectations++
        } else if (appraisal.question_1 === 'exceeds-expectations') {
          exceedsExpectations++
        }
      } else if (appraisal.status === 'pending') {
        draftCount++
      }
    })
    
    // Get total employees to calculate not started
    const employees = await getEmployees()
    const totalEmployees = employees.length
    const notStarted = totalEmployees - submittedCount - draftCount

    const stats = {
      total: totalEmployees,
      belowExpectations,
      meetsExpectations,
      exceedsExpectations,
      notStarted: Math.max(0, notStarted), // Ensure not negative
      submittedCount,
      draftCount,
    }
    
    // Cache the result with appropriate TTL for performance stats
    cache.set(cacheKey, stats, CACHE_DURATIONS.PERFORMANCE_STATS)
    // console.log('📊 Cached performance stats for 2 minutes')
    
    return stats
  } catch (error) {
    console.error('Failed to fetch performance statistics:', error)
    return {
      total: 0,
      belowExpectations: 0,
      meetsExpectations: 0,
      exceedsExpectations: 0,
      notStarted: 0,
      submittedCount: 0,
      draftCount: 0,
    }
  }
}

export async function getManagerPerformanceStats(): Promise<PerformanceStats> {
  try {
    const { getCurrentUser } = await import('../auth')
    const currentUser = await getCurrentUser()

    if (!currentUser) {
      console.error('🚨 [PERF STATS] No authenticated user found')
      return {
        total: 0,
        belowExpectations: 0,
        meetsExpectations: 0,
        exceedsExpectations: 0,
        notStarted: 0,
        submittedCount: 0,
        draftCount: 0,
      }
    }

    console.log('📊 [PERF STATS] Getting performance stats for user:', {
      id: currentUser.id,
      fullName: currentUser.fullName,
      role: currentUser.role
    })

    // Check cache first (unique per manager)
    const cacheKey = `manager-performance-stats-${currentUser.id}`
    const cachedStats = cache.get<PerformanceStats>(cacheKey)
    if (cachedStats) {
      // console.log('📊 Using cached manager performance stats')
      return cachedStats
    }

    // Get the current active period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)

    console.log('📅 [PERF STATS] Available periods:', periods.map(p => ({
      id: p.id,
      start: p.periodStart,
      end: p.periodEnd,
      closed: p.closed
    })))

    if (!currentPeriod) {
      console.warn('🚨 [PERF STATS] No active appraisal period found')
      return {
        total: 0,
        belowExpectations: 0,
        meetsExpectations: 0,
        exceedsExpectations: 0,
        notStarted: 0,
        submittedCount: 0,
        draftCount: 0,
      }
    }

    console.log('✅ [PERF STATS] Using active period:', {
      id: currentPeriod.id,
      start: currentPeriod.periodStart,
      end: currentPeriod.periodEnd
    })

    // Get all appraisals for the current manager and period
    // Super-admins see all appraisals, managers see only their own
    const { hasSuperAdminAccess } = await import('../auth')
    const appraisals = hasSuperAdminAccess(currentUser)
      ? await db.getPerformanceStatsByPeriod(currentPeriod.id)
      : await db.getAllAppraisalsForManager(currentUser.id, currentPeriod.id)

    console.log('📋 [PERF STATS] Found appraisals for manager:', appraisals.map(a => ({
      employee_id: a.employee_id,
      question_1: a.question_1,
      status: a.status,
      submitted_at: a.submitted_at
    })))

    // Initialize counters
    let belowExpectations = 0
    let meetsExpectations = 0
    let exceedsExpectations = 0
    let submittedCount = 0
    let draftCount = 0
    
    // Count appraisals by q1 response and status
    appraisals.forEach(appraisal => {
      if (appraisal.status === 'submitted') {
        submittedCount++
        
        // Only count q1 responses for submitted appraisals
        if (appraisal.question_1 === 'below-expectations') {
          belowExpectations++
        } else if (appraisal.question_1 === 'meets-expectations') {
          meetsExpectations++
        } else if (appraisal.question_1 === 'exceeds-expectations') {
          exceedsExpectations++
        }
      } else if (appraisal.status === 'pending') {
        draftCount++
      }
    })
    
    // Get total employees managed by this manager
    // Super-admins see all employees, managers/senior-managers see their hierarchical employees
    const managedEmployees = hasSuperAdminAccess(currentUser)
      ? await getEmployees() // Super-admin sees all employees
      : await getEmployeesForManager(currentUser.id, currentUser.role) // Manager/Senior-manager sees hierarchical employees
    const totalManagedEmployees = managedEmployees.length
    const notStarted = totalManagedEmployees - submittedCount - draftCount

    console.log('👥 [PERF STATS] Manager employee summary:', {
      totalManagedEmployees,
      submittedCount,
      draftCount,
      notStarted,
      belowExpectations,
      meetsExpectations,
      exceedsExpectations
    })

    const stats = {
      total: totalManagedEmployees,
      belowExpectations,
      meetsExpectations,
      exceedsExpectations,
      notStarted: Math.max(0, notStarted), // Ensure not negative
      submittedCount,
      draftCount,
    }
    
    // Cache the result with appropriate TTL for performance stats
    cache.set(cacheKey, stats, CACHE_DURATIONS.PERFORMANCE_STATS)
    // console.log('📊 Cached manager performance stats for 2 minutes')
    
    return stats
  } catch (error) {
    console.error('Failed to fetch manager performance statistics:', error)
    return {
      total: 0,
      belowExpectations: 0,
      meetsExpectations: 0,
      exceedsExpectations: 0,
      notStarted: 0,
      submittedCount: 0,
      draftCount: 0,
    }
  }
}