import { getCurrentUser } from "@/lib/auth"
import { getPendingApprovalsForUser, getApprovalStatistics } from "@/lib/data/approvals"
import { MultiLevelApprovalDashboard } from "@/components/multi-level-approval-dashboard"
import { redirect } from "next/navigation"

export default async function MultiLevelApprovalsPage() {
  const currentUser = await getCurrentUser()
  
  if (!currentUser) {
    redirect('/sign-in')
  }

  // Check if user has approval permissions
  if (!['manager', 'senior-manager', 'hr-admin', 'admin', 'super-admin'].includes(currentUser.role)) {
    redirect('/dashboard')
  }

  // Fetch pending approvals and statistics
  const [pendingApprovals, statistics] = await Promise.all([
    getPendingApprovalsForUser(currentUser.id, currentUser.email),
    getApprovalStatistics(currentUser.id)
  ])

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex flex-col gap-4">
        <h1 className="text-3xl font-bold">Appraisal Approvals</h1>
        <p className="text-muted-foreground">
          Review and approve submitted employee appraisals. Approvals follow the organizational hierarchy and workflow system.
        </p>
      </div>

      <MultiLevelApprovalDashboard 
        pendingApprovals={pendingApprovals}
        statistics={statistics}
      />

      {/* Information Section */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">How Multi-Level Approvals Work</h3>
          <div className="space-y-3 text-sm text-muted-foreground">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium text-xs mt-0.5">
                1
              </div>
              <div>
                <strong>Level 1:</strong> Direct manager reviews and approves the appraisal
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-medium text-xs mt-0.5">
                2
              </div>
              <div>
                <strong>Level 2:</strong> Senior manager (manager's manager) provides final approval
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center text-green-600 font-medium text-xs mt-0.5">
                ✓
              </div>
              <div>
                <strong>Completed:</strong> Appraisal is fully approved and ready for payment processing
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Approval Guidelines</h3>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>• Review appraisals thoroughly before approving</p>
            <p>• Provide clear rejection reasons when declining</p>
            <p>• Add comments to help the next approver understand context</p>
            <p>• Urgent items (7+ days pending) should be prioritized</p>
            <p>• Overdue items (14+ days) require immediate attention</p>
          </div>
        </div>
      </div>
    </div>
  )
}
