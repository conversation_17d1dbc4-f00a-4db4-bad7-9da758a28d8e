import { Suspense } from "react"
import { Calculator, DollarSign, FileText, AlertCircle } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { AccountingDashboardTable } from "@/components/accounting-dashboard-table"
import { AccountingStatsCards, AccountingStatsCardsSkeleton, AccountingSummary } from "@/components/accounting-stats-cards"
import { RoleGuard } from "@/components/role-guard"
import { getCurrentUser } from "@/lib/auth"
import { getAccountingDataForUser, getAccountingStats } from "@/lib/data/accounting"
import { getPeriods } from "@/lib/data/periods"

// Loading component for the accounting data
function AccountingDataSkeleton() {
  return (
    <div className="space-y-6">
      <AccountingStatsCardsSkeleton />
      <Card>
        <CardHeader>
          <div className="h-6 w-48 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-4 w-64 bg-gray-200 rounded animate-pulse"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-12 bg-gray-100 rounded animate-pulse"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Main accounting content component
async function AccountingContent() {
  console.log('[ACCOUNTING] Loading accounting dashboard content')
  
  try {
    // Load accounting data and stats in parallel
    const [accountingData, accountingStats, periods] = await Promise.all([
      getAccountingDataForUser(),
      getAccountingStats(),
      getPeriods()
    ])
    
    // Get current active period
    const currentPeriod = periods.find(p => !p.closed)
    
    // Create a display name for the current period
    const periodDisplayName = currentPeriod
      ? `${new Date(currentPeriod.periodStart).toLocaleDateString()} - ${new Date(currentPeriod.periodEnd).toLocaleDateString()}`
      : 'No active period'

    console.log('[ACCOUNTING] Loaded data:', {
      employees: accountingData.length,
      stats: accountingStats,
      currentPeriod: periodDisplayName
    })

    if (accountingData.length === 0) {
      return (
        <div className="space-y-6">
          <Card className="text-center py-12">
            <CardContent>
              <Calculator className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No employees found</h3>
              <p className="text-muted-foreground mb-6">
                You don't have people to process payments for right now. Please add employees or check your permissions.
              </p>
              <Badge variant="outline" className="bg-blue-100 text-blue-800">
                <FileText className="mr-1 h-3 w-3" />
                Accounting View
              </Badge>
            </CardContent>
          </Card>
        </div>
      )
    }

    return (
      <div className="space-y-6">
        {/* Summary Overview */}
        <AccountingSummary stats={accountingStats} />
        
        {/* Stats Cards */}
        <AccountingStatsCards stats={accountingStats} />
        
        {/* Main Data Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-green-600" />
                  Employee Payment Data
                </CardTitle>
                <CardDescription>
                  {currentPeriod ? `${periodDisplayName} - ` : ''}
                  Manage employee payments and export payroll data
                </CardDescription>
              </div>
              <Badge variant="outline" className="bg-green-100 text-green-800">
                <Calculator className="mr-1 h-3 w-3" />
                {accountingData.length} Employee{accountingData.length !== 1 ? 's' : ''}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <AccountingDashboardTable data={accountingData} />
          </CardContent>
        </Card>
        
        {/* Help Information */}
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-800 flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Export Information
            </CardTitle>
          </CardHeader>
          <CardContent className="text-blue-700">
            <div className="space-y-2 text-sm">
              <p>• <strong>Ready to Pay:</strong> Employees approved for payment processing</p>
              <p>• <strong>CSV Export:</strong> Only includes employees ready for payment or with submitted appraisals</p>
              <p>• <strong>Hours:</strong> Automatically calculated for hourly employees (160 hours standard)</p>
              <p>• <strong>Payment Status:</strong> Based on appraisal submission and approval status</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  } catch (error) {
    console.error('[ACCOUNTING] Error loading accounting data:', error)
    
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load accounting data. Please try refreshing the page or contact support if the issue persists.
          </AlertDescription>
        </Alert>
      </div>
    )
  }
}

// Main page component
export default async function AccountingPage() {
  console.log('[ACCOUNTING] Loading accounting page')
  
  const user = await getCurrentUser()

  if (!user) {
    return (
      <div className="p-4 sm:p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Authentication required. Please sign in to access the accounting dashboard.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  console.log('[ACCOUNTING] User authenticated:', {
    fullName: user.fullName,
    role: user.role,
    id: user.id
  })

  return (
    <RoleGuard
      allowedRoles={['accountant', 'hr-admin', 'super-admin']}
      userRole={user.role}
      redirectTo="/dashboard"
    >
      <div className="p-4 sm:p-6 space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Accounting Dashboard</h1>
            <p className="text-muted-foreground">
              Manage employee payments and export payroll data for the current appraisal period
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge 
              variant={user.role === 'super-admin' ? 'default' : 'secondary'}
              className="flex items-center gap-1"
            >
              <Calculator className="h-3 w-3" />
              {user.role === 'super-admin' ? 'Super Admin' : 'Accountant'}
            </Badge>
          </div>
        </div>

        {/* Main Content */}
        <Suspense fallback={<AccountingDataSkeleton />}>
          <AccountingContent />
        </Suspense>
      </div>
    </RoleGuard>
  )
}
